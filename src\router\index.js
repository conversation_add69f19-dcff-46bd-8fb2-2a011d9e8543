import { createRouter, createWebHistory } from 'vue-router'

// 导入组件
const Home = () => import('../views/Home.vue')
const About = () => import('../views/About.vue')

// 定义路由
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home
  },
  {
    path: '/about',
    name: 'About',
    component: About
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(),
  routes
})

export default router