# Vue 3 + Vite + Router + Axios 项目

这是一个使用Vue 3、Vite、Vue Router和Axios构建的单页应用。

## 项目结构
```
├── src/
│   ├── views/
│   │   ├── Home.vue
│   │   └── About.vue
│   ├── router/
│   │   └── index.js
│   ├── App.vue
│   └── main.js
├── package.json
└── vite.config.js
```

## 功能特点
- 使用Vue 3的Composition API和setup语法
- 集成Vue Router实现路由功能
- 集成Axios用于HTTP请求
- 使用Vite作为构建工具

## 安装和启动

1. 打开命令提示符(cmd.exe)，导航到项目目录
2. 运行以下命令安装依赖：
   ```
   npm install
   ```
3. 安装完成后，运行以下命令启动开发服务器：
   ```
   npm run dev
   ```

## 注意事项
- 项目使用的是Vue 3的Composition API和setup语法，不使用TypeScript
- Home.vue组件中包含了Axios的使用示例
- 路由配置在router/index.js文件中

## 相关链接
- [Vue 3 文档](https://vuejs.org/)
- [Vite 文档](https://vite.dev/)
- [Vue Router 文档](https://router.vuejs.org/)
- [Axios 文档](https://axios-http.com/)
