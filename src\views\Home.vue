<template>
  <div class="home">
    <h1>首页</h1>
    <p>欢迎来到我的Vue 3应用！</p>
    <button @click="fetchData">获取数据</button>
    
    <h2>员工信息表</h2>
    <el-table :data="tableData" style="width: 100%"   highlight-current-row @row-click="clickRow"                    >
        <el-table-column type="selection" width="40">
        <template #default="scope">
            <el-radio v-model="radio" :label="scope.$index" @change="handleRadioChange(scope.$index, scope.row)"></el-radio>
        </template>
    </el-table-column>
      <el-table-column prop="id" label="ID" width="80" />
      <el-table-column prop="name" label="姓名" width="120" />
      <el-table-column prop="age" label="年龄" width="80" />
      <el-table-column prop="email" label="邮箱" />
      <el-table-column prop="department" label="部门" width="120" />
    </el-table>
  </div>
</template>


<script setup>
// 可以在这里导入axios并使用
import axios from 'axios'
import { ref, onMounted } from 'vue'

// 示例：使用axios获取数据
const fetchData = async () => {
  try {
    const response = await axios.get('https://jsonplaceholder.typicode.com/posts/1')
    console.log(response.data)
  } catch (error) {
    console.error('Error fetching data:', error)
  }
}

// 创建假数据
const tableData = ref([
  {
    id: 1,
    name: '张三',
    age: 25,
    email: '<EMAIL>',
    department: '技术部'
  },
  {
    id: 2,
    name: '李四',
    age: 30,
    email: '<EMAIL>',
    department: '市场部'
  },
  {
    id: 3,
    name: '王五',
    age: 28,
    email: '<EMAIL>',
    department: '人事部'
  },
  {
    id: 4,
    name: '赵六',
    age: 35,
    email: '<EMAIL>',
    department: '财务部'
  },
  {
    id: 5,
    name: '钱七',
    age: 27,
    email: '<EMAIL>',
    department: '技术部'
  }
])

const radio = ref('');
const currentRow = ref({})
/**选择行数据 */
function clickRow(row, column, event) {
    console.log(event);
    
    currentRow.value = row;
}

// 选中单选框  这里不处理数据
// function handleClick(index) {
//     radio.value = index;    
// }


function handleRadioChange(index, row) {
    radio.value = index;
    currentRow.value = row;
}
// 在组件挂载时获取数据
onMounted(() => {
  fetchData()
})
</script>





<style scoped>
.home {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  color: #333;
}

button {
  padding: 8px 16px;
  background-color: #42b883;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:hover {
  background-color: #3aa876;
}
</style>